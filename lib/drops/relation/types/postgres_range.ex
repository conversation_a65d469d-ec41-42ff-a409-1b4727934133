defmodule Drops.Relation.Types.PostgresRange do
  @moduledoc """
  Custom Ecto type for PostgreSQL range types.

  This type handles PostgreSQL range types like int4range, int8range, numrange,
  tsrange, tstzrange, and daterange by working with Postgrex.Range structs.

  ## Supported Range Types

  - `:int4range` - Integer ranges
  - `:int8range` - Bigint ranges  
  - `:numrange` - Numeric ranges
  - `:tsrange` - Timestamp ranges
  - `:tstzrange` - Timestamp with timezone ranges
  - `:daterange` - Date ranges
  - `:generic` - Generic range type for custom ranges

  ## Examples

      # In a schema
      field :availability, {:parameterized, Drops.Relation.Types.PostgresRange, :tsrange}
      field :price_range, {:parameterized, Drops.Relation.Types.PostgresRange, :numrange}

      # Working with ranges
      range = %Postgrex.Range{lower: 1, upper: 10, lower_inclusive: true, upper_inclusive: false}
      
  """

  use Ecto.ParameterizedType

  @impl true
  def type(_params), do: :string

  @impl true
  def init(range_type) when range_type in [:int4range, :int8range, :numrange, :tsrange, :tstzrange, :daterange, :generic] do
    range_type
  end

  def init(range_type) do
    raise ArgumentError, "unsupported range type: #{inspect(range_type)}"
  end

  @impl true
  def cast(value, range_type) do
    case value do
      %Postgrex.Range{} = range ->
        {:ok, range}

      # Handle string representation of ranges like "[1,10)"
      value when is_binary(value) ->
        case parse_range_string(value, range_type) do
          {:ok, range} -> {:ok, range}
          :error -> :error
        end

      # Handle tuple representation {lower, upper}
      {lower, upper} ->
        {:ok, %Postgrex.Range{
          lower: lower,
          upper: upper,
          lower_inclusive: true,
          upper_inclusive: false
        }}

      nil ->
        {:ok, nil}

      _ ->
        :error
    end
  end

  @impl true
  def load(value, _loader, range_type) when is_binary(value) do
    case parse_range_string(value, range_type) do
      {:ok, range} -> {:ok, range}
      :error -> :error
    end
  end

  def load(%Postgrex.Range{} = range, _loader, _range_type) do
    {:ok, range}
  end

  def load(nil, _loader, _range_type) do
    {:ok, nil}
  end

  def load(_, _loader, _range_type) do
    :error
  end

  @impl true
  def dump(value, _dumper, _range_type) do
    case value do
      %Postgrex.Range{} = range ->
        {:ok, range}

      nil ->
        {:ok, nil}

      _ ->
        :error
    end
  end

  @impl true
  def equal?(a, b, _range_type) do
    a == b
  end

  # Parse PostgreSQL range string format like "[1,10)" or "(,5]"
  defp parse_range_string(value, range_type) when is_binary(value) do
    value = String.trim(value)

    cond do
      value == "empty" ->
        {:ok, %Postgrex.Range{lower: nil, upper: nil, lower_inclusive: false, upper_inclusive: false}}

      String.match?(value, ~r/^[\[\(].*[\]\)]$/) ->
        parse_bounded_range(value, range_type)

      true ->
        :error
    end
  end

  defp parse_bounded_range(value, range_type) do
    # Extract bounds and inclusivity
    {lower_char, rest} = String.split_at(value, 1)
    {content, upper_char} = String.split_at(rest, String.length(rest) - 1)

    lower_inclusive = lower_char == "["
    upper_inclusive = upper_char == "]"

    case String.split(content, ",", parts: 2) do
      [lower_str, upper_str] ->
        lower = parse_bound_value(String.trim(lower_str), range_type)
        upper = parse_bound_value(String.trim(upper_str), range_type)

        {:ok, %Postgrex.Range{
          lower: lower,
          upper: upper,
          lower_inclusive: lower_inclusive,
          upper_inclusive: upper_inclusive
        }}

      _ ->
        :error
    end
  end

  defp parse_bound_value("", _range_type), do: nil

  defp parse_bound_value(value, range_type) do
    case range_type do
      :int4range ->
        case Integer.parse(value) do
          {int, ""} -> int
          _ -> value
        end

      :int8range ->
        case Integer.parse(value) do
          {int, ""} -> int
          _ -> value
        end

      :numrange ->
        case Decimal.parse(value) do
          {decimal, ""} -> decimal
          _ -> 
            case Float.parse(value) do
              {float, ""} -> float
              _ -> value
            end
        end

      :daterange ->
        case Date.from_iso8601(value) do
          {:ok, date} -> date
          _ -> value
        end

      :tsrange ->
        case NaiveDateTime.from_iso8601(value) do
          {:ok, datetime} -> datetime
          _ -> value
        end

      :tstzrange ->
        case DateTime.from_iso8601(value) do
          {:ok, datetime, _offset} -> datetime
          _ -> value
        end

      :generic ->
        value
    end
  end
end
