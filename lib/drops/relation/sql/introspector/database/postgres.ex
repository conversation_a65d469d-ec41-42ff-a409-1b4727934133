defmodule Drops.Relation.SQL.Introspector.Database.Postgres do
  @moduledoc """
  PostgreSQL implementation of the Database behavior for schema introspection.

  This module provides PostgreSQL-specific implementations for database introspection
  operations using PostgreSQL's system catalogs and information schema.

  ## Features

  - Index introspection via system catalogs
  - Column metadata extraction via information schema
  - PostgreSQL type to Ecto type conversion
  - Support for various PostgreSQL index types (btree, hash, gin, gist, brin)
  """

  @behaviour Drops.Relation.SQL.Introspector.Database

  alias Drops.Relation.Schema.{Index, Indices}

  @impl true
  def get_table_indices(repo, table_name) do
    query = """
    SELECT
        i.relname as index_name,
        array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
        ix.indisunique as is_unique,
        am.amname as index_type
    FROM pg_class t
    JOIN pg_index ix ON t.oid = ix.indrelid
    JOIN pg_class i ON i.oid = ix.indexrelid
    JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
    JOIN pg_am am ON i.relam = am.oid
    WHERE t.relname = $1
      AND NOT ix.indisprimary  -- Exclude primary key indices
    GROUP BY i.relname, ix.indisunique, am.amname
    ORDER BY i.relname
    """

    case repo.query(query, [table_name]) do
      {:ok, %{rows: rows}} ->
        indices =
          for [index_name, column_names, is_unique, index_type] <- rows do
            field_names = Enum.map(column_names, &String.to_atom/1)
            type = index_type_to_atom(index_type)

            Index.from_names(index_name, field_names, is_unique, type)
          end

        {:ok, Indices.new(indices)}

      {:error, error} ->
        {:error, error}
    end
  end

  @impl true
  def introspect_table_columns(repo, table_name) do
    # Use PostgreSQL system catalogs to get accurate type information including arrays
    query = """
    SELECT
        a.attname as column_name,
        CASE
            -- PostgreSQL Array Types (internal names start with _)
            WHEN t.typname = '_int2' THEN 'smallint[]'
            WHEN t.typname = '_int4' THEN 'integer[]'
            WHEN t.typname = '_int8' THEN 'bigint[]'
            WHEN t.typname = '_float4' THEN 'real[]'
            WHEN t.typname = '_float8' THEN 'double precision[]'
            WHEN t.typname = '_numeric' THEN 'numeric[]'
            WHEN t.typname = '_bool' THEN 'boolean[]'
            WHEN t.typname = '_text' THEN 'text[]'
            WHEN t.typname = '_varchar' THEN 'character varying[]'
            WHEN t.typname = '_bpchar' THEN 'character[]'
            WHEN t.typname = '_char' THEN 'character[]'
            WHEN t.typname = '_bytea' THEN 'bytea[]'
            WHEN t.typname = '_uuid' THEN 'uuid[]'
            WHEN t.typname = '_date' THEN 'date[]'
            WHEN t.typname = '_time' THEN 'time[]'
            WHEN t.typname = '_timetz' THEN 'time with time zone[]'
            WHEN t.typname = '_timestamp' THEN 'timestamp[]'
            WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
            WHEN t.typname = '_interval' THEN 'interval[]'
            WHEN t.typname = '_json' THEN 'json[]'
            WHEN t.typname = '_jsonb' THEN 'jsonb[]'
            -- PostgreSQL Scalar Types (internal names)
            WHEN t.typname = 'int2' THEN 'smallint'
            WHEN t.typname = 'int4' THEN 'integer'
            WHEN t.typname = 'int8' THEN 'bigint'
            WHEN t.typname = 'float4' THEN 'real'
            WHEN t.typname = 'float8' THEN 'double precision'
            WHEN t.typname = 'bpchar' THEN 'character'
            WHEN t.typname = 'varchar' THEN 'character varying'
            WHEN t.typname = 'bool' THEN 'boolean'
            -- Keep standard PostgreSQL type names as-is
            ELSE t.typname
        END as data_type,
        CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as is_nullable,
        pg_get_expr(ad.adbin, ad.adrelid) as column_default,
        CASE
            WHEN pk.attname IS NOT NULL THEN true
            ELSE false
        END as is_primary_key
    FROM pg_attribute a
    JOIN pg_type t ON a.atttypid = t.oid
    JOIN pg_class c ON a.attrelid = c.oid
    JOIN pg_namespace n ON c.relnamespace = n.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN (
        SELECT a.attname
        FROM pg_index i
        JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
        JOIN pg_class c ON i.indrelid = c.oid
        JOIN pg_namespace n ON c.relnamespace = n.oid
        WHERE i.indisprimary
        AND c.relname = $1
        AND n.nspname = 'public'
    ) pk ON a.attname = pk.attname
    WHERE c.relname = $1
        AND n.nspname = 'public'
        AND a.attnum > 0
        AND NOT a.attisdropped
    ORDER BY a.attnum
    """

    case repo.query(query, [table_name]) do
      {:ok, %{rows: rows}} ->
        Enum.map(rows, fn [
                            column_name,
                            data_type,
                            is_nullable,
                            _column_default,
                            is_primary_key
                          ] ->
          %{
            name: column_name,
            type: data_type,
            not_null: is_nullable == "NO",
            primary_key: is_primary_key
          }
        end)

      {:error, error} ->
        raise "Failed to introspect table #{table_name}: #{inspect(error)}"
    end
  end

  @impl true
  def db_type_to_ecto_type(postgres_type, field_name) do
    # Handle array types first (since we can't use String.ends_with? in guards)
    if String.ends_with?(postgres_type, "[]") do
      base_type = String.trim_trailing(postgres_type, "[]")
      {:array, db_type_to_ecto_type(base_type, field_name)}
    else
      downcased = String.downcase(postgres_type)
      convert_base_type(downcased, field_name)
    end
  end

  # Convert non-array PostgreSQL types to Ecto types
  defp convert_base_type(postgres_type, _field_name) do
    case postgres_type do
      # Integer types and aliases
      "integer" ->
        :integer

      "int" ->
        :integer

      "int4" ->
        :integer

      "bigint" ->
        :integer

      "int8" ->
        :integer

      "smallint" ->
        :integer

      "int2" ->
        :integer

      # Serial types and aliases
      "serial" ->
        :id

      "serial4" ->
        :id

      "bigserial" ->
        :id

      "serial8" ->
        :id

      "smallserial" ->
        :id

      "serial2" ->
        :id

      # Floating point types and aliases
      "real" ->
        :float

      "float4" ->
        :float

      "double precision" ->
        :float

      "float8" ->
        :float

      # Decimal types
      "numeric" ->
        :decimal

      "decimal" ->
        :decimal

      "money" ->
        :decimal

      # String types
      "text" ->
        :string

      "character varying" ->
        :string

      "varchar" ->
        :string

      "char" ->
        :string

      "character" ->
        :string

      # PostgreSQL internal name type
      "name" ->
        :string

      # Boolean type
      "boolean" ->
        :boolean

      # Binary types
      "bytea" ->
        :binary

      # Date/time types
      "date" ->
        :date

      "time" ->
        :time

      "time without time zone" ->
        :time

      "time with time zone" ->
        :time

      "timetz" ->
        :time

      "timestamp without time zone" ->
        :naive_datetime

      "timestamp" ->
        :naive_datetime

      "timestamp with time zone" ->
        :utc_datetime

      "timestamptz" ->
        :utc_datetime

      # JSON types
      "json" ->
        :map

      "jsonb" ->
        :map

      # UUID type
      "uuid" ->
        :binary_id

      # XML type
      "xml" ->
        :string

      # Network types (mapped to string for now, could be custom types later)
      "inet" ->
        :string

      "cidr" ->
        :string

      "macaddr" ->
        :string

      # Geometric types (mapped to string for now, could be custom types later)
      "point" ->
        :string

      "line" ->
        :string

      "lseg" ->
        :string

      "box" ->
        :string

      "path" ->
        :string

      "polygon" ->
        :string

      "circle" ->
        :string

      # Interval type (could be custom type later)
      "interval" ->
        :string

      # PostgreSQL Range Types - map to EctoRange types
      "int4range" ->
        EctoRange.IntegerRange

      "int8range" ->
        EctoRange.IntegerRange

      "numrange" ->
        EctoRange.DecimalRange

      "tsrange" ->
        EctoRange.NaiveDateTimeRange

      "tstzrange" ->
        EctoRange.DateTimeRange

      "daterange" ->
        EctoRange.DateRange

      # Handle complex types that need pattern matching
      type ->
        cond do
          String.ends_with?(type, "range") ->
            :string

          String.starts_with?(type, "character varying(") ->
            :string

          String.starts_with?(type, "varchar(") ->
            :string

          String.starts_with?(type, "character(") ->
            :string

          String.starts_with?(type, "char(") ->
            :string

          String.starts_with?(type, "numeric(") ->
            :decimal

          String.starts_with?(type, "decimal(") ->
            :decimal

          true ->
            :string
        end
    end
  end

  @impl true
  def index_type_to_atom(type_string) do
    case String.downcase(type_string) do
      "btree" -> :btree
      "hash" -> :hash
      "gin" -> :gin
      "gist" -> :gist
      "brin" -> :brin
      "spgist" -> :spgist
      _ -> nil
    end
  end
end
